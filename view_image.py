#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的图片查看器
"""

import os
import sys
from PIL import Image
import tkinter as tk
from tkinter import ttk
from PIL import ImageTk

def view_image(image_path):
    """显示图片"""
    if not os.path.exists(image_path):
        print(f"图片文件不存在: {image_path}")
        return
    
    # 创建主窗口
    root = tk.Tk()
    root.title(f"图片查看器 - {os.path.basename(image_path)}")
    
    # 加载图片
    try:
        image = Image.open(image_path)
        
        # 获取屏幕尺寸
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        
        # 计算缩放比例
        max_width = min(screen_width - 100, 800)
        max_height = min(screen_height - 100, 600)
        
        # 计算缩放比例
        width_ratio = max_width / image.width
        height_ratio = max_height / image.height
        scale_ratio = min(width_ratio, height_ratio, 1.0)  # 不放大
        
        # 缩放图片
        if scale_ratio < 1.0:
            new_width = int(image.width * scale_ratio)
            new_height = int(image.height * scale_ratio)
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 转换为tkinter可用的格式
        photo = ImageTk.PhotoImage(image)
        
        # 创建标签显示图片
        label = tk.Label(root, image=photo)
        label.pack(padx=10, pady=10)
        
        # 显示图片信息
        info_frame = ttk.Frame(root)
        info_frame.pack(fill='x', padx=10, pady=5)
        
        info_text = f"文件: {os.path.basename(image_path)} | 尺寸: {image.width}x{image.height}"
        info_label = ttk.Label(info_frame, text=info_text)
        info_label.pack()
        
        # 设置窗口大小和位置
        window_width = image.width + 20
        window_height = image.height + 80
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 运行主循环
        root.mainloop()
        
    except Exception as e:
        print(f"无法打开图片: {e}")

if __name__ == "__main__":
    # 默认查看最新生成的星座图片
    default_path = "videoFile/horoscope/test_horoscope_20250615.jpg"
    
    if len(sys.argv) > 1:
        image_path = sys.argv[1]
    else:
        image_path = default_path
    
    print(f"正在查看图片: {image_path}")
    view_image(image_path)
