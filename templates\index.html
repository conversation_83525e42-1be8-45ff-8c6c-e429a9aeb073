<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Social Auto Upload - 后端服务</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
        }
        h1 {
            color: #333;
            margin-bottom: 1rem;
        }
        .status {
            color: #28a745;
            font-size: 1.2rem;
            margin: 1rem 0;
        }
        .info {
            color: #666;
            margin: 1rem 0;
        }
        .link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px;
            transition: background 0.3s;
        }
        .link:hover {
            background: #0056b3;
        }
        .api-list {
            text-align: left;
            margin: 1rem 0;
        }
        .api-item {
            background: #f8f9fa;
            padding: 0.5rem;
            margin: 0.5rem 0;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Social Auto Upload</h1>
        <div class="status">✅ 后端服务运行中</div>
        
        <div class="info">
            <p>后端API服务已启动，端口: <strong>5409</strong></p>
            <p>前端界面请访问: <a href="http://localhost:5173" class="link">http://localhost:5173</a></p>
        </div>
        
        <div class="api-list">
            <h3>主要API端点:</h3>
            <div class="api-item">GET /getFiles - 获取文件列表</div>
            <div class="api-item">GET /getValidAccounts - 获取账号列表</div>
            <div class="api-item">POST /upload - 上传文件</div>
            <div class="api-item">POST /postVideo - 发布视频</div>
            <div class="api-item">GET /login - 账号登录</div>
        </div>
        
        <div class="info">
            <p>🌟 新增功能: AI星座运势自动发布</p>
            <p>运行测试: <code>python test_horoscope.py</code></p>
            <p>启动定时任务: <code>python horoscope/scheduler.py</code></p>
        </div>
    </div>
</body>
</html>
