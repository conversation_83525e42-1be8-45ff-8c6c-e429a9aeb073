# 🌟 星座运势自动发布系统

基于 social-auto-upload 项目的 AI 星座运势自动生成和发布系统。

## ✨ 功能特点

- 🤖 **AI 内容生成**: 使用 OpenAI GPT 自动生成 12 星座运势内容
- 🎨 **精美图片生成**: Python 自动生成精美的星座运势图片
- ⏰ **定时自动发布**: 每天下午 5 点生成，第二天早上 8 点发布到小红书
- 🎯 **完全自动化**: 无需人工干预，全自动运行
- 📱 **小红书适配**: 图片尺寸和格式完全适配小红书平台

## 🚀 快速开始

### 1. 配置 API 密钥

编辑 `horoscope/config.py` 文件，填入您的 OpenAI API 密钥：

```python
AI_CONFIG = {
    'api_key': 'your-openai-api-key-here',  # 替换为您的实际 API 密钥
    'model': 'gpt-3.5-turbo',
    'base_url': 'https://api.openai.com/v1',
}
```

### 2. 运行系统

```bash
# 方式一：使用启动脚本（推荐）
python start_horoscope.py

# 方式二：直接运行测试
python test_horoscope.py

# 方式三：启动定时任务
python horoscope/scheduler.py
```

### 3. 配置小红书账号

确保在 `cookiesFile` 目录下有小红书账号的登录信息文件。

## 📁 项目结构

```
horoscope/
├── __init__.py          # 模块初始化
├── config.py            # 配置文件
├── ai_generator.py      # AI 内容生成器
├── image_generator.py   # 图片生成器
└── scheduler.py         # 定时任务调度器

videoFile/horoscope/     # 生成的图片和数据存储目录
├── horoscope_20240615.jpg
├── horoscope_data_20240615.json
└── ...

start_horoscope.py       # 启动脚本
test_horoscope.py        # 测试脚本
```

## ⚙️ 配置说明

### AI 配置 (AI_CONFIG)

```python
AI_CONFIG = {
    'provider': 'openai',           # AI 提供商
    'api_key': 'your-key',          # API 密钥
    'model': 'gpt-3.5-turbo',       # 模型名称
    'base_url': 'https://...',      # API 基础 URL
}
```

### 图片配置 (IMAGE_CONFIG)

```python
IMAGE_CONFIG = {
    'width': 1080,                  # 图片宽度
    'height': 1350,                 # 图片高度 (4:5 比例)
    'background_color': '#f8f9fa',  # 背景颜色
    'title_font_size': 48,          # 标题字体大小
    'content_font_size': 28,        # 内容字体大小
}
```

### 发布配置 (PUBLISH_CONFIG)

```python
PUBLISH_CONFIG = {
    'schedule_time': '17:00',       # 每天生成时间
    'publish_time': '08:00',        # 第二天发布时间
    'title_template': '🌟 {date} 十二星座运势 🌟',
    'tags': ['星座运势', '每日运势', '星座', '占卜'],
    'enable_auto_publish': True,    # 是否启用自动发布
}
```

## 🎯 使用流程

### 自动模式（推荐）

1. 配置好 API 密钥和小红书账号
2. 运行 `python horoscope/scheduler.py`
3. 系统将在每天下午 5 点自动：
   - 调用 AI 生成第二天的星座运势内容
   - 生成精美的图片
   - 安排第二天早上 8 点发布到小红书

### 手动模式

1. 运行 `python start_horoscope.py`
2. 选择 "手动生成今日运势"
3. 系统立即生成运势图片

### 测试模式

1. 运行 `python test_horoscope.py`
2. 测试各个模块是否正常工作

## 📸 生成的图片特点

- **尺寸**: 1080x1350 (小红书推荐的 4:5 比例)
- **内容**: 包含 12 星座的完整运势信息
- **设计**: 精美的卡片式布局，每个星座独立显示
- **信息**: 整体运势、爱情、事业、财运、健康五个维度

## 🔧 自定义配置

### 修改发布时间

在 `horoscope/config.py` 中修改：

```python
PUBLISH_CONFIG = {
    'schedule_time': '18:00',  # 改为下午6点生成
    'publish_time': '07:00',   # 改为早上7点发布
}
```

### 修改标签

```python
PUBLISH_CONFIG = {
    'tags': ['星座', '运势', '占卜', '每日运势', '十二星座', '星座运势'],
}
```

### 修改图片样式

在 `horoscope/config.py` 中的 `IMAGE_CONFIG` 部分修改颜色、字体大小等。

## 🐛 常见问题

### 1. API 密钥错误

**问题**: 提示 API 密钥无效
**解决**: 检查 `horoscope/config.py` 中的 `api_key` 是否正确

### 2. 图片生成失败

**问题**: 图片生成时出错
**解决**: 检查 `videoFile/horoscope` 目录是否有写入权限

### 3. 小红书发布失败

**问题**: 无法发布到小红书
**解决**: 确保 `cookiesFile` 目录下有有效的账号登录信息

### 4. 字体显示问题

**问题**: 生成的图片中文字显示异常
**解决**: 系统会自动使用默认字体，如需自定义字体，请在 `horoscope/config.py` 中配置字体路径

## 📝 开发说明

### 添加新的 AI 提供商

1. 在 `ai_generator.py` 中添加新的 AI 客户端
2. 修改 `config.py` 中的 `AI_CONFIG`
3. 实现对应的 API 调用逻辑

### 自定义图片模板

1. 修改 `image_generator.py` 中的绘制逻辑
2. 在 `config.py` 中添加新的模板配置
3. 支持背景图片、自定义字体等

### 扩展发布平台

1. 参考现有的小红书发布逻辑
2. 在 `scheduler.py` 中添加新平台的发布方法
3. 配置对应的账号信息

## 📄 许可证

本项目基于原 social-auto-upload 项目，遵循相同的开源许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

---

**享受自动化的星座运势发布吧！** 🌟✨
