# -*- coding: utf-8 -*-
"""
星座运势系统测试脚本
"""

import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from horoscope.ai_generator import HoroscopeAIGenerator
from horoscope.image_generator import HoroscopeImageGenerator
from horoscope.scheduler import HoroscopeScheduler


def test_ai_generator():
    """测试AI生成器"""
    print("🤖 测试AI星座运势生成器...")
    
    try:
        generator = HoroscopeAIGenerator()
        
        # 生成明天的运势
        tomorrow = datetime.now() + timedelta(days=1)
        horoscope_data = generator.generate_daily_horoscope(tomorrow)
        
        if horoscope_data:
            print(f"✅ AI生成成功，共生成 {len(horoscope_data)} 个星座的运势")
            
            # 显示第一个星座的数据作为示例
            first_sign = list(horoscope_data.keys())[0]
            first_data = horoscope_data[first_sign]
            print(f"\n📋 示例 - {first_data['name']} {first_data['emoji']}:")
            print(f"   整体运势: {first_data['overall']}")
            print(f"   爱情运势: {first_data['love']}")
            print(f"   事业运势: {first_data['career']}")
            print(f"   财运: {first_data['money']}")
            print(f"   健康: {first_data['health']}")
            
            return horoscope_data
        else:
            print("❌ AI生成失败")
            return None
            
    except Exception as e:
        print(f"❌ AI生成器测试失败: {e}")
        return None


def test_image_generator(horoscope_data=None):
    """测试图片生成器"""
    print("\n🎨 测试图片生成器...")
    
    try:
        generator = HoroscopeImageGenerator()
        
        # 如果没有提供数据，使用测试数据
        if horoscope_data is None:
            horoscope_data = get_test_horoscope_data()
        
        # 生成图片
        output_dir = Path("videoFile/horoscope")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        date_str = datetime.now().strftime('%Y%m%d')
        output_path = output_dir / f"test_horoscope_{date_str}.jpg"
        
        result_path = generator.generate_horoscope_image(horoscope_data, str(output_path))
        
        if os.path.exists(result_path):
            print(f"✅ 图片生成成功: {result_path}")
            print(f"   图片大小: {os.path.getsize(result_path)} 字节")
            return result_path
        else:
            print("❌ 图片生成失败")
            return None
            
    except Exception as e:
        print(f"❌ 图片生成器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_scheduler():
    """测试调度器"""
    print("\n⏰ 测试调度器...")
    
    try:
        scheduler = HoroscopeScheduler()
        
        # 手动执行一次生成任务
        result = scheduler.manual_generate()
        
        if result:
            print("✅ 调度器测试成功")
        else:
            print("❌ 调度器测试失败")
            
        return result
        
    except Exception as e:
        print(f"❌ 调度器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def get_test_horoscope_data():
    """获取测试用的星座数据"""
    from horoscope.config import ZODIAC_SIGNS
    
    test_data = {}
    test_content = {
        'overall': '今日运势平稳，适合稳步前进，保持乐观心态。',
        'love': '感情方面需要多一些耐心和理解，真诚沟通很重要。',
        'career': '工作上专注当下任务，细心处理每个细节会有好结果。',
        'money': '财运较为平稳，避免冲动消费，理性规划支出。',
        'health': '身体状况良好，注意劳逸结合，保持规律作息。'
    }
    
    for sign_key, sign_info in ZODIAC_SIGNS.items():
        test_data[sign_key] = {
            'name': sign_info['name'],
            'emoji': sign_info['emoji'],
            'date_range': sign_info['date_range'],
            'date': datetime.now().strftime('%Y-%m-%d'),
            **test_content
        }
    
    return test_data


def main():
    """主测试函数"""
    print("🚀 开始星座运势系统测试")
    print("=" * 50)
    
    # 检查配置
    print("📋 检查配置...")
    from horoscope.config import AI_CONFIG
    
    if AI_CONFIG['api_key'] == 'your-openai-api-key-here':
        print("⚠️  警告: 请先在 horoscope/config.py 中配置您的 OpenAI API Key")
        print("   当前将使用测试数据进行图片生成测试")
        use_ai = False
    else:
        print("✅ 配置检查通过")
        use_ai = True
    
    # 测试AI生成器
    horoscope_data = None
    if use_ai:
        horoscope_data = test_ai_generator()
    
    # 测试图片生成器
    image_path = test_image_generator(horoscope_data)
    
    # 测试调度器（仅在有AI配置时）
    if use_ai:
        test_scheduler()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成!")
    
    if image_path:
        print(f"📸 生成的图片位置: {image_path}")
        print("💡 您可以打开图片查看效果")
    
    if not use_ai:
        print("\n📝 下一步:")
        print("1. 在 horoscope/config.py 中配置您的 OpenAI API Key")
        print("2. 运行 python test_horoscope.py 进行完整测试")
        print("3. 运行 python horoscope/scheduler.py 启动定时任务")


if __name__ == "__main__":
    main()
