#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的12星座运势图片生成
"""

from horoscope.image_generator import HoroscopeImageGenerator
from horoscope.config import ZODIAC_SIGNS
from datetime import datetime
import os

def create_full_test_data():
    """创建完整的12星座测试数据"""
    today = datetime.now().strftime('%Y-%m-%d')
    
    # 运势描述模板
    overall_templates = [
        "今日运势不错，保持积极心态",
        "稳定发展的一天，适合规划未来",
        "思维活跃，创意满满的一天",
        "情绪稳定，适合处理重要事务",
        "充满活力，适合挑战新事物",
        "细心谨慎，注意细节的一天"
    ]
    
    love_templates = [
        "感情方面需要多沟通",
        "桃花运不错，单身者有机会",
        "与伴侣关系和谐",
        "避免争吵，多一些理解",
        "浪漫的一天，适合约会",
        "感情稳定，享受甜蜜时光"
    ]
    
    career_templates = [
        "工作上要专注细节",
        "团队合作顺利",
        "创意项目有突破",
        "领导力得到认可",
        "学习新技能的好时机",
        "工作效率很高"
    ]
    
    money_templates = [
        "财运平稳，理性消费",
        "投资需谨慎，多做功课",
        "收入稳定，可适当储蓄",
        "偏财运不错，但要量力而行",
        "理财规划需要调整",
        "支出控制得当"
    ]
    
    health_templates = [
        "注意休息，保持健康",
        "身体状况良好",
        "适合运动锻炼",
        "注意饮食均衡",
        "精神状态饱满",
        "避免过度劳累"
    ]
    
    horoscope_data = {}
    
    for i, (sign_key, sign_info) in enumerate(ZODIAC_SIGNS.items()):
        horoscope_data[sign_key] = {
            'name': sign_info['name'],
            'emoji': sign_info['emoji'],
            'date_range': sign_info['date_range'],
            'date': today,
            'overall': overall_templates[i % len(overall_templates)],
            'love': love_templates[i % len(love_templates)],
            'career': career_templates[i % len(career_templates)],
            'money': money_templates[i % len(money_templates)],
            'health': health_templates[i % len(health_templates)]
        }
    
    return horoscope_data

def test_full_horoscope():
    """测试完整的12星座运势图片生成"""
    print("🌟 开始生成完整的12星座运势图片...")
    print("=" * 50)
    
    try:
        # 创建测试数据
        print("📋 创建12星座测试数据...")
        horoscope_data = create_full_test_data()
        print(f"✅ 已创建 {len(horoscope_data)} 个星座的数据")
        
        # 显示星座列表
        print("\n🔮 包含的星座:")
        for sign_key, sign_data in horoscope_data.items():
            print(f"   {sign_data['emoji']} {sign_data['name']} ({sign_data['date_range']})")
        
        # 生成图片
        print(f"\n🎨 开始生成图片...")
        generator = HoroscopeImageGenerator()
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = f'videoFile/horoscope/full_horoscope_{timestamp}.jpg'
        
        print(f"📍 输出路径: {output_path}")
        result = generator.generate_horoscope_image(horoscope_data, output_path)
        
        if os.path.exists(result):
            file_size = os.path.getsize(result)
            print(f"\n✅ 完整星座运势图片生成成功!")
            print(f"📁 文件路径: {result}")
            print(f"📊 文件大小: {file_size:,} 字节")
            
            # 检查文件是否有效
            if file_size > 50000:  # 至少50KB
                print("🎉 图片文件大小正常，包含完整的12星座信息!")
                
                # 创建HTML预览页面
                create_preview_page(result)
                
                return True
            else:
                print("⚠️  图片文件太小，可能生成有问题")
                return False
        else:
            print("❌ 图片文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 生成图片时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_preview_page(image_path):
    """创建预览页面"""
    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整星座运势图片预览</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            align-items: center;
        }}
        .container {{
            max-width: 1200px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
            margin: 20px;
        }}
        .title {{
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }}
        .image-container {{
            text-align: center;
            margin: 20px 0;
        }}
        .horoscope-image {{
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }}
        .info {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }}
        .status {{
            color: #28a745;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🌟 完整12星座运势图片预览 🌟</h1>
        
        <div class="info">
            <p><span class="status">✅ 完整星座运势图片生成成功！</span></p>
            <p>📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>📍 文件位置: {image_path}</p>
            <p>🎨 包含完整的12星座运势信息</p>
            <p>📱 图片尺寸: 1080x1350 (适合小红书发布)</p>
        </div>
        
        <div class="image-container">
            <img src="{image_path}" 
                 alt="完整星座运势图片" 
                 class="horoscope-image"
                 onerror="this.style.display='none'; document.getElementById('error-msg').style.display='block';">
            
            <div id="error-msg" style="display:none; color:red; padding:20px;">
                ❌ 无法加载图片，请检查文件路径是否正确
            </div>
        </div>
        
        <div class="info">
            <h3>🎯 测试结果</h3>
            <ul>
                <li>✅ 字体显示正常 (使用微软雅黑)</li>
                <li>✅ 包含12个星座完整信息</li>
                <li>✅ 图片尺寸符合小红书要求</li>
                <li>✅ 布局美观，信息清晰</li>
            </ul>
        </div>
    </div>
</body>
</html>"""
    
    with open('full_horoscope_preview.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"📱 预览页面已创建: full_horoscope_preview.html")

if __name__ == "__main__":
    success = test_full_horoscope()
    if success:
        print("\n🎉 完整测试成功!")
        print("📱 可以打开 full_horoscope_preview.html 查看完整效果")
        print("🚀 现在可以配置 OpenAI API 进行 AI 生成测试了")
    else:
        print("\n❌ 测试失败，请检查错误信息")
