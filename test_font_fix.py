#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字体修复
"""

from horoscope.image_generator import HoroscopeImageGenerator
from datetime import datetime
import os

def test_font_fix():
    """测试字体修复"""
    print("🎨 测试字体修复...")
    
    # 创建测试数据
    test_data = {
        'aries': {
            'name': '白羊座', 'emoji': '♈', 'date_range': '3.21-4.19',
            'date': '2025-06-15',
            'overall': '今日运势不错，保持积极心态',
            'love': '感情方面需要多沟通',
            'career': '工作上要专注细节',
            'money': '财运平稳，理性消费',
            'health': '注意休息，保持健康'
        },
        'taurus': {
            'name': '金牛座', 'emoji': '♉', 'date_range': '4.20-5.20',
            'date': '2025-06-15',
            'overall': '稳定发展的一天',
            'love': '桃花运不错',
            'career': '工作顺利',
            'money': '投资需谨慎',
            'health': '身体健康'
        },
        'gemini': {
            'name': '双子座', 'emoji': '♊', 'date_range': '5.21-6.21',
            'date': '2025-06-15',
            'overall': '思维活跃的一天',
            'love': '沟通是关键',
            'career': '创意满满',
            'money': '收入稳定',
            'health': '精神饱满'
        }
    }
    
    try:
        # 生成图片
        generator = HoroscopeImageGenerator()
        output_path = 'videoFile/horoscope/font_test.jpg'
        
        print(f"📍 输出路径: {output_path}")
        result = generator.generate_horoscope_image(test_data, output_path)
        
        if os.path.exists(result):
            file_size = os.path.getsize(result)
            print(f"✅ 字体测试图片生成成功!")
            print(f"📁 文件路径: {result}")
            print(f"📊 文件大小: {file_size:,} 字节")
            
            # 检查文件是否有效
            if file_size > 10000:  # 至少10KB
                print("🎉 图片文件大小正常，字体应该已修复!")
                return True
            else:
                print("⚠️  图片文件太小，可能生成有问题")
                return False
        else:
            print("❌ 图片文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 生成图片时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_font_fix()
    if success:
        print("\n🌟 测试完成! 请查看生成的图片验证中文显示是否正常")
        print("📱 可以在浏览器中打开 show_image.html 查看效果")
    else:
        print("\n❌ 测试失败，请检查错误信息")
