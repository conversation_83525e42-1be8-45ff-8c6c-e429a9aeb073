<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整星座运势图片预览</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            max-width: 1200px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
            margin: 20px;
        }
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        .horoscope-image {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .status {
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🌟 完整12星座运势图片预览 🌟</h1>
        
        <div class="info">
            <p><span class="status">✅ 完整星座运势图片生成成功！</span></p>
            <p>📅 生成时间: 2025-06-15 12:58:33</p>
            <p>📍 文件位置: videoFile/horoscope/full_horoscope_20250615_125833.jpg</p>
            <p>🎨 包含完整的12星座运势信息</p>
            <p>📱 图片尺寸: 1080x1350 (适合小红书发布)</p>
        </div>
        
        <div class="image-container">
            <img src="videoFile/horoscope/full_horoscope_20250615_125833.jpg" 
                 alt="完整星座运势图片" 
                 class="horoscope-image"
                 onerror="this.style.display='none'; document.getElementById('error-msg').style.display='block';">
            
            <div id="error-msg" style="display:none; color:red; padding:20px;">
                ❌ 无法加载图片，请检查文件路径是否正确
            </div>
        </div>
        
        <div class="info">
            <h3>🎯 测试结果</h3>
            <ul>
                <li>✅ 字体显示正常 (使用微软雅黑)</li>
                <li>✅ 包含12个星座完整信息</li>
                <li>✅ 图片尺寸符合小红书要求</li>
                <li>✅ 布局美观，信息清晰</li>
            </ul>
        </div>
    </div>
</body>
</html>