# -*- coding: utf-8 -*-
"""
AI星座运势内容生成器
"""

import openai
import json
from datetime import datetime, timedelta
from typing import Dict, List
from .config import ZODIAC_SIGNS, AI_CONFIG


class HoroscopeAIGenerator:
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=AI_CONFIG['api_key'],
            base_url=AI_CONFIG.get('base_url')
        )
        self.model = AI_CONFIG['model']
    
    def generate_daily_horoscope(self, target_date: datetime = None) -> Dict[str, Dict]:
        """
        生成指定日期的12星座运势
        
        Args:
            target_date: 目标日期，默认为明天
            
        Returns:
            Dict: 包含12星座运势的字典
        """
        if target_date is None:
            target_date = datetime.now() + timedelta(days=1)
        
        date_str = target_date.strftime('%Y年%m月%d日')
        
        prompt = self._create_prompt(date_str)
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一位专业的星座运势分析师，擅长为每个星座提供准确、积极、有指导意义的运势预测。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=2000
            )
            
            content = response.choices[0].message.content
            horoscope_data = self._parse_ai_response(content, target_date)
            
            return horoscope_data
            
        except Exception as e:
            print(f"AI生成运势失败: {e}")
            return self._get_fallback_horoscope(target_date)
    
    def _create_prompt(self, date_str: str) -> str:
        """创建AI提示词"""
        return f"""
请为{date_str}生成十二星座的运势预测。

要求：
1. 每个星座的运势包含：整体运势、爱情运势、事业运势、财运、健康运势
2. 每项运势控制在20-30字以内
3. 语言要积极正面，给人希望和指导
4. 避免过于具体的预测，多用建议性语言
5. 请按照以下JSON格式返回：

{{
    "aries": {{
        "overall": "整体运势内容",
        "love": "爱情运势内容", 
        "career": "事业运势内容",
        "money": "财运内容",
        "health": "健康运势内容"
    }},
    "taurus": {{
        "overall": "整体运势内容",
        "love": "爱情运势内容",
        "career": "事业运势内容", 
        "money": "财运内容",
        "health": "健康运势内容"
    }},
    ... (其他10个星座)
}}

请确保返回的是有效的JSON格式。
"""
    
    def _parse_ai_response(self, content: str, target_date: datetime) -> Dict[str, Dict]:
        """解析AI返回的内容"""
        try:
            # 尝试提取JSON部分
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = content[start_idx:end_idx]
                horoscope_raw = json.loads(json_str)
                
                # 格式化数据
                horoscope_data = {}
                for sign_key, data in horoscope_raw.items():
                    if sign_key in ZODIAC_SIGNS:
                        horoscope_data[sign_key] = {
                            'name': ZODIAC_SIGNS[sign_key]['name'],
                            'emoji': ZODIAC_SIGNS[sign_key]['emoji'],
                            'date_range': ZODIAC_SIGNS[sign_key]['date_range'],
                            'overall': data.get('overall', '今日运势不错，保持积极心态。'),
                            'love': data.get('love', '感情方面需要多沟通。'),
                            'career': data.get('career', '工作上要专注细节。'),
                            'money': data.get('money', '财运平稳，理性消费。'),
                            'health': data.get('health', '注意休息，保持健康。'),
                            'date': target_date.strftime('%Y-%m-%d')
                        }
                
                return horoscope_data
                
        except Exception as e:
            print(f"解析AI响应失败: {e}")
        
        return self._get_fallback_horoscope(target_date)
    
    def _get_fallback_horoscope(self, target_date: datetime) -> Dict[str, Dict]:
        """获取备用运势内容"""
        fallback_data = {}
        
        fallback_content = {
            'overall': '今日运势平稳，适合稳步前进，保持乐观心态。',
            'love': '感情方面需要多一些耐心和理解，真诚沟通很重要。',
            'career': '工作上专注当下任务，细心处理每个细节会有好结果。',
            'money': '财运较为平稳，避免冲动消费，理性规划支出。',
            'health': '身体状况良好，注意劳逸结合，保持规律作息。'
        }
        
        for sign_key, sign_info in ZODIAC_SIGNS.items():
            fallback_data[sign_key] = {
                'name': sign_info['name'],
                'emoji': sign_info['emoji'],
                'date_range': sign_info['date_range'],
                'date': target_date.strftime('%Y-%m-%d'),
                **fallback_content
            }
        
        return fallback_data


# 使用示例
if __name__ == "__main__":
    generator = HoroscopeAIGenerator()
    horoscope = generator.generate_daily_horoscope()
    print(json.dumps(horoscope, ensure_ascii=False, indent=2))
