#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的发布测试 - 验证基本功能
"""

import os
import sys
import sqlite3
import shutil
from datetime import datetime
import asyncio

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """测试基本功能"""
    print("🔧 基本功能测试")
    print("=" * 50)
    
    # 1. 检查账号
    print("📱 检查账号信息...")
    try:
        conn = sqlite3.connect('db/user_info.db')
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM user_info WHERE type = 1 AND status = 1')
        account = cursor.fetchone()
        conn.close()
        
        if account:
            print(f"✅ 找到有效账号: {account[3]}")
            print(f"   Cookie文件: {account[2]}")
        else:
            print("❌ 没有找到有效账号")
            return False
    except Exception as e:
        print(f"❌ 检查账号失败: {e}")
        return False
    
    # 2. 检查Cookie文件
    print("\n📄 检查Cookie文件...")
    cookie_path = f"cookiesFile/{account[2]}"
    if os.path.exists(cookie_path):
        file_size = os.path.getsize(cookie_path)
        print(f"✅ Cookie文件存在: {cookie_path}")
        print(f"   文件大小: {file_size:,} 字节")
    else:
        print(f"❌ Cookie文件不存在: {cookie_path}")
        return False
    
    # 3. 检查图片文件
    print("\n🖼️  检查图片文件...")
    image_dir = "videoFile/horoscope"
    if os.path.exists(image_dir):
        image_files = [f for f in os.listdir(image_dir) if f.endswith('.jpg')]
        if image_files:
            latest_image = max(image_files, key=lambda x: os.path.getmtime(os.path.join(image_dir, x)))
            image_path = os.path.join(image_dir, latest_image)
            image_size = os.path.getsize(image_path)
            print(f"✅ 找到图片文件: {latest_image}")
            print(f"   文件大小: {image_size:,} 字节")
        else:
            print("❌ 没有找到图片文件")
            return False
    else:
        print(f"❌ 图片目录不存在: {image_dir}")
        return False
    
    # 4. 检查依赖模块
    print("\n📦 检查依赖模块...")
    try:
        from uploader.xiaohongshu_uploader.main import XiaoHongShuVideo, cookie_auth
        print("✅ 小红书上传模块导入成功")
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        return False
    
    try:
        from playwright.async_api import async_playwright
        print("✅ Playwright模块导入成功")
    except ImportError as e:
        print(f"❌ Playwright模块导入失败: {e}")
        return False
    
    # 5. 测试Cookie验证
    print("\n🔐 测试Cookie验证...")
    try:
        async def test_cookie():
            result = await cookie_auth(cookie_path)
            return result
        
        cookie_valid = asyncio.run(test_cookie())
        if cookie_valid:
            print("✅ Cookie验证成功")
        else:
            print("❌ Cookie验证失败 - 可能需要重新登录")
            return False
    except Exception as e:
        print(f"❌ Cookie验证出错: {e}")
        return False
    
    print("\n🎉 基本功能测试完成!")
    return True

def test_content_preparation():
    """测试内容准备"""
    print("\n📝 内容准备测试")
    print("=" * 30)
    
    # 准备发布内容
    today = datetime.now().strftime('%Y年%m月%d日')
    content = {
        'title': f'🌟 {today} 十二星座运势 🌟',
        'desc': f'''✨ {today} 星座运势预测 ✨

🔮 今日为您带来十二星座的详细运势分析
💫 包含整体运势、爱情、事业、财运、健康五大方面

⭐ 每日更新，仅供娱乐参考
🌙 愿你每天都有好运气！

#星座运势 #每日运势 #星座 #占卜 #运势预测 #十二星座''',
        'tags': ['星座运势', '每日运势', '星座', '占卜', '运势预测', '十二星座']
    }
    
    print(f"✅ 标题: {content['title']}")
    print(f"✅ 描述长度: {len(content['desc'])} 字符")
    print(f"✅ 标签数量: {len(content['tags'])} 个")
    
    return content

def main():
    """主函数"""
    print("🚀 小红书发布功能测试")
    print("=" * 60)
    
    # 基本功能测试
    basic_ok = test_basic_functionality()
    if not basic_ok:
        print("\n❌ 基本功能测试失败，请检查配置")
        return
    
    # 内容准备测试
    content = test_content_preparation()
    
    print("\n📊 测试总结:")
    print("=" * 30)
    print("✅ 账号配置: 正常")
    print("✅ Cookie文件: 存在且有效")
    print("✅ 图片文件: 准备就绪")
    print("✅ 依赖模块: 导入成功")
    print("✅ 内容准备: 完成")
    
    print("\n🎯 下一步建议:")
    print("1. 手动测试小红书登录状态")
    print("2. 检查小红书创作者中心是否可以正常访问")
    print("3. 确认账号有发布权限")
    print("4. 如果一切正常，可以尝试手动发布测试")
    
    print("\n💡 如果要进行真实发布测试:")
    print("   python test_xiaohongshu_publish.py")
    
    print("\n🌟 所有基础功能测试通过!")

if __name__ == "__main__":
    main()
