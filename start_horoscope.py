# -*- coding: utf-8 -*-
"""
星座运势系统启动脚本
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))


def show_menu():
    """显示菜单"""
    print("\n🌟 星座运势自动发布系统 🌟")
    print("=" * 40)
    print("1. 🧪 运行系统测试")
    print("2. 🔧 手动生成今日运势")
    print("3. ⏰ 启动定时任务 (每天下午5点)")
    print("4. ⚙️  配置系统设置")
    print("5. 📊 查看历史记录")
    print("0. 🚪 退出")
    print("=" * 40)


def run_test():
    """运行测试"""
    print("\n🧪 运行系统测试...")
    os.system("python test_horoscope.py")


def manual_generate():
    """手动生成运势"""
    print("\n🔧 手动生成星座运势...")
    
    try:
        from horoscope.scheduler import HoroscopeScheduler
        
        scheduler = HoroscopeScheduler()
        result = scheduler.manual_generate()
        
        if result:
            print("✅ 手动生成成功!")
        else:
            print("❌ 生成失败，请检查配置")
            
    except Exception as e:
        print(f"❌ 生成失败: {e}")


def start_scheduler():
    """启动定时任务"""
    print("\n⏰ 启动定时任务...")
    print("系统将在每天下午5点自动生成第二天的星座运势")
    print("按 Ctrl+C 可以停止定时任务")
    
    try:
        from horoscope.scheduler import HoroscopeScheduler
        
        scheduler = HoroscopeScheduler()
        scheduler.start_scheduler()
        
    except Exception as e:
        print(f"❌ 启动定时任务失败: {e}")


def configure_system():
    """配置系统"""
    print("\n⚙️  系统配置")
    print("-" * 30)
    
    config_file = "horoscope/config.py"
    
    print(f"📁 配置文件位置: {config_file}")
    print("\n主要配置项:")
    print("1. AI_CONFIG['api_key'] - OpenAI API密钥")
    print("2. PUBLISH_CONFIG['schedule_time'] - 生成时间 (默认: 17:00)")
    print("3. PUBLISH_CONFIG['publish_time'] - 发布时间 (默认: 08:00)")
    print("4. PUBLISH_CONFIG['tags'] - 小红书标签")
    
    choice = input("\n是否打开配置文件进行编辑? (y/N): ")
    if choice.lower() == 'y':
        try:
            os.system(f"notepad {config_file}")
        except:
            print(f"请手动编辑文件: {config_file}")


def view_history():
    """查看历史记录"""
    print("\n📊 历史记录")
    print("-" * 30)
    
    history_dir = Path("videoFile/horoscope")
    
    if not history_dir.exists():
        print("暂无历史记录")
        return
    
    # 列出所有生成的文件
    image_files = list(history_dir.glob("*.jpg"))
    data_files = list(history_dir.glob("*.json"))
    
    print(f"📸 生成的图片: {len(image_files)} 个")
    for img in sorted(image_files)[-5:]:  # 显示最近5个
        size = img.stat().st_size
        print(f"   {img.name} ({size} 字节)")
    
    print(f"\n📋 运势数据: {len(data_files)} 个")
    for data in sorted(data_files)[-5:]:  # 显示最近5个
        print(f"   {data.name}")
    
    if image_files:
        choice = input("\n是否打开最新的图片? (y/N): ")
        if choice.lower() == 'y':
            latest_image = sorted(image_files)[-1]
            try:
                os.startfile(str(latest_image))
            except:
                print(f"请手动打开: {latest_image}")


def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择操作 (0-5): ").strip()
            
            if choice == '0':
                print("👋 再见!")
                break
            elif choice == '1':
                run_test()
            elif choice == '2':
                manual_generate()
            elif choice == '3':
                start_scheduler()
            elif choice == '4':
                configure_system()
            elif choice == '5':
                view_history()
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
        
        input("\n按回车键继续...")


if __name__ == "__main__":
    main()
