# -*- coding: utf-8 -*-
"""
星座运势定时任务调度器
"""

import os
import sys
import schedule
import time
import json
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from horoscope.ai_generator import HoroscopeAIGenerator
from horoscope.image_generator import HoroscopeImageGenerator
from horoscope.config import PUBLISH_CONFIG
from uploader.xiaohongshu_uploader.main import XiaoHongShuVideo
from utils.files_times import generate_schedule_time_next_day


class HoroscopeScheduler:
    def __init__(self):
        self.ai_generator = HoroscopeAIGenerator()
        self.image_generator = HoroscopeImageGenerator()
        self.video_file_dir = project_root / "videoFile"
        self.horoscope_dir = self.video_file_dir / "horoscope"
        
        # 确保目录存在
        self.horoscope_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"星座运势调度器已初始化")
        print(f"图片保存目录: {self.horoscope_dir}")
    
    def generate_and_schedule_horoscope(self):
        """生成星座运势并安排发布"""
        try:
            print(f"开始生成星座运势 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 1. 生成明天的星座运势内容
            tomorrow = datetime.now() + timedelta(days=1)
            horoscope_data = self.ai_generator.generate_daily_horoscope(tomorrow)
            
            if not horoscope_data:
                print("❌ AI生成星座运势失败")
                return False
            
            print(f"✅ AI生成星座运势成功，共{len(horoscope_data)}个星座")
            
            # 2. 生成图片
            date_str = tomorrow.strftime('%Y%m%d')
            image_filename = f"horoscope_{date_str}.jpg"
            image_path = self.horoscope_dir / image_filename
            
            generated_image = self.image_generator.generate_horoscope_image(
                horoscope_data, 
                str(image_path)
            )
            
            print(f"✅ 星座运势图片生成成功: {generated_image}")
            
            # 3. 保存运势数据（用于后续分析）
            data_filename = f"horoscope_data_{date_str}.json"
            data_path = self.horoscope_dir / data_filename
            
            with open(data_path, 'w', encoding='utf-8') as f:
                json.dump(horoscope_data, f, ensure_ascii=False, indent=2)
            
            # 4. 安排小红书发布
            if PUBLISH_CONFIG['enable_auto_publish']:
                self.schedule_xiaohongshu_publish(generated_image, tomorrow)
            
            print(f"🎉 星座运势生成和调度完成!")
            return True
            
        except Exception as e:
            print(f"❌ 生成星座运势时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def schedule_xiaohongshu_publish(self, image_path: str, target_date: datetime):
        """安排小红书发布"""
        try:
            # 生成发布标题
            date_str = target_date.strftime('%m月%d日')
            title = PUBLISH_CONFIG['title_template'].format(date=date_str)
            
            # 计算发布时间（明天早上8点）
            publish_time = target_date.replace(
                hour=int(PUBLISH_CONFIG['publish_time'].split(':')[0]),
                minute=int(PUBLISH_CONFIG['publish_time'].split(':')[1]),
                second=0,
                microsecond=0
            )
            
            # 创建小红书发布对象
            # 注意：这里需要指定账号文件路径
            account_file = "cookiesFile/xiaohongshu_account.json"  # 请确保账号文件存在
            
            xhs_video = XiaoHongShuVideo(
                title=title,
                file_path=image_path,
                tags=PUBLISH_CONFIG['tags'],
                publish_date=publish_time,
                account_file=account_file,
                thumbnail_path=None  # 图片发布不需要缩略图
            )
            
            print(f"📅 已安排小红书发布:")
            print(f"   标题: {title}")
            print(f"   发布时间: {publish_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   图片路径: {image_path}")
            print(f"   标签: {', '.join(PUBLISH_CONFIG['tags'])}")
            
            # 这里可以将发布任务添加到数据库或队列中
            # 具体实现取决于项目的发布机制
            
        except Exception as e:
            print(f"❌ 安排小红书发布时发生错误: {e}")
    
    def start_scheduler(self):
        """启动定时任务"""
        # 每天下午5点执行
        schedule.every().day.at(PUBLISH_CONFIG['schedule_time']).do(
            self.generate_and_schedule_horoscope
        )
        
        print(f"⏰ 定时任务已启动")
        print(f"   执行时间: 每天 {PUBLISH_CONFIG['schedule_time']}")
        print(f"   发布时间: 第二天 {PUBLISH_CONFIG['publish_time']}")
        print(f"   按 Ctrl+C 停止调度器")
        
        # 可选：立即执行一次测试
        if input("是否立即执行一次测试？(y/N): ").lower() == 'y':
            print("🧪 执行测试运行...")
            self.generate_and_schedule_horoscope()
        
        # 开始调度循环
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            print("\n⏹️  定时任务已停止")
    
    def manual_generate(self, target_date: datetime = None):
        """手动生成星座运势（用于测试）"""
        if target_date is None:
            target_date = datetime.now() + timedelta(days=1)
        
        print(f"🔧 手动生成 {target_date.strftime('%Y-%m-%d')} 的星座运势")
        return self.generate_and_schedule_horoscope()


def main():
    """主函数"""
    scheduler = HoroscopeScheduler()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--test':
        # 测试模式
        print("🧪 测试模式")
        scheduler.manual_generate()
    else:
        # 正常调度模式
        scheduler.start_scheduler()


if __name__ == "__main__":
    main()
