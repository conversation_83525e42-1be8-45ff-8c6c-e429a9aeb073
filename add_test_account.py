#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加测试账号到数据库
"""

import sqlite3
import json
import os

def add_test_account():
    """添加测试账号"""
    print("📱 添加测试账号到数据库...")
    
    # 检查cookie文件
    cookie_file = "954f719d-49a1-11f0-b2ac-2e9811625013.json"
    cookie_path = f"cookiesFile/{cookie_file}"
    
    if not os.path.exists(cookie_path):
        print(f"❌ Cookie文件不存在: {cookie_path}")
        return False
    
    # 尝试读取cookie文件获取用户名
    try:
        with open(cookie_path, 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        
        # 尝试从cookie中提取用户名
        username = "小红书用户"  # 默认用户名
        
        # 如果cookie中有用户信息，尝试提取
        if isinstance(cookie_data, list):
            for cookie in cookie_data:
                if isinstance(cookie, dict) and 'name' in cookie:
                    if 'user' in cookie['name'].lower():
                        username = cookie.get('value', username)
                        break
        
        print(f"📄 Cookie文件大小: {os.path.getsize(cookie_path):,} 字节")
        
    except Exception as e:
        print(f"⚠️  无法读取cookie文件: {e}")
        username = "测试用户"
    
    try:
        # 连接数据库
        conn = sqlite3.connect('db/user_info.db')
        cursor = conn.cursor()
        
        # 检查是否已存在相同的记录
        cursor.execute('SELECT * FROM user_info WHERE filePath = ?', (cookie_file,))
        existing = cursor.fetchone()
        
        if existing:
            print(f"✅ 账号已存在，更新状态为有效")
            cursor.execute('''
                UPDATE user_info 
                SET status = 1, userName = ?
                WHERE filePath = ?
            ''', (username, cookie_file))
        else:
            print(f"➕ 添加新账号记录")
            # 插入账号记录
            # type: 1=小红书, filePath: cookie文件名, userName: 用户名, status: 1=有效
            cursor.execute('''
                INSERT INTO user_info (type, filePath, userName, status)
                VALUES (?, ?, ?, ?)
            ''', (1, cookie_file, username, 1))
        
        conn.commit()
        
        # 验证插入结果
        cursor.execute('SELECT * FROM user_info')
        rows = cursor.fetchall()
        
        print(f"\n✅ 账号添加成功!")
        print(f"📋 当前数据库中的账号:")
        for row in rows:
            print(f"   ID: {row[0]}, 平台: {row[1]}, Cookie: {row[2]}, 用户名: {row[3]}, 状态: {row[4]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 添加账号时出错: {e}")
        return False

if __name__ == "__main__":
    success = add_test_account()
    if success:
        print(f"\n🎉 测试账号添加成功!")
        print(f"🚀 现在可以进行发布测试了")
    else:
        print(f"\n❌ 添加测试账号失败")
