#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
星座运势自动化系统完整测试
"""

import os
import sys
import sqlite3
import shutil
from datetime import datetime, timedelta
import asyncio

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_horoscope_generation():
    """测试星座运势生成"""
    print("🔮 测试星座运势生成...")
    
    try:
        from horoscope.image_generator import HoroscopeImageGenerator
        from horoscope.config import ZODIAC_SIGNS
        
        # 创建测试数据
        today = datetime.now().strftime('%Y-%m-%d')
        horoscope_data = {}
        
        for sign_key, sign_info in ZODIAC_SIGNS.items():
            horoscope_data[sign_key] = {
                'name': sign_info['name'],
                'emoji': sign_info['emoji'],
                'date_range': sign_info['date_range'],
                'date': today,
                'overall': f'{sign_info["name"]}今日运势不错',
                'love': '感情方面需要多沟通',
                'career': '工作上要专注细节',
                'money': '财运平稳，理性消费',
                'health': '注意休息，保持健康'
            }
        
        # 生成图片
        generator = HoroscopeImageGenerator()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = f'videoFile/horoscope/auto_test_{timestamp}.jpg'
        
        result = generator.generate_horoscope_image(horoscope_data, output_path)
        
        if os.path.exists(result):
            file_size = os.path.getsize(result)
            print(f"✅ 星座运势图片生成成功")
            print(f"   文件路径: {result}")
            print(f"   文件大小: {file_size:,} 字节")
            return result
        else:
            print("❌ 图片生成失败")
            return None
            
    except Exception as e:
        print(f"❌ 星座运势生成出错: {e}")
        return None

def test_account_status():
    """测试账号状态"""
    print("\n📱 测试账号状态...")
    
    try:
        conn = sqlite3.connect('db/user_info.db')
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM user_info WHERE type = 1 AND status = 1')
        accounts = cursor.fetchall()
        conn.close()
        
        if accounts:
            print(f"✅ 找到 {len(accounts)} 个有效小红书账号")
            for account in accounts:
                cookie_path = f"cookiesFile/{account[2]}"
                if os.path.exists(cookie_path):
                    print(f"   📄 {account[3]}: Cookie文件存在")
                else:
                    print(f"   ❌ {account[3]}: Cookie文件缺失")
            return accounts
        else:
            print("❌ 没有找到有效账号")
            return None
            
    except Exception as e:
        print(f"❌ 检查账号状态出错: {e}")
        return None

async def test_cookie_validation(accounts):
    """测试Cookie验证"""
    print("\n🔐 测试Cookie验证...")
    
    try:
        from uploader.xiaohongshu_uploader.main import cookie_auth
        
        valid_accounts = []
        for account in accounts:
            cookie_path = f"cookiesFile/{account[2]}"
            print(f"   验证账号: {account[3]}")
            
            try:
                is_valid = await cookie_auth(cookie_path)
                if is_valid:
                    print(f"   ✅ {account[3]}: Cookie有效")
                    valid_accounts.append(account)
                else:
                    print(f"   ❌ {account[3]}: Cookie已失效")
            except Exception as e:
                print(f"   ❌ {account[3]}: 验证出错 - {e}")
        
        return valid_accounts
        
    except Exception as e:
        print(f"❌ Cookie验证出错: {e}")
        return []

def test_content_preparation(image_path):
    """测试内容准备"""
    print("\n📝 测试内容准备...")
    
    if not image_path or not os.path.exists(image_path):
        print("❌ 图片文件不存在")
        return None
    
    today = datetime.now().strftime('%Y年%m月%d日')
    content = {
        'title': f'🌟 {today} 十二星座运势 🌟',
        'desc': f'''✨ {today} 星座运势预测 ✨

🔮 今日为您带来十二星座的详细运势分析
💫 包含整体运势、爱情、事业、财运、健康五大方面

⭐ 每日更新，仅供娱乐参考
🌙 愿你每天都有好运气！

#星座运势 #每日运势 #星座 #占卜 #运势预测 #十二星座''',
        'image_path': image_path,
        'tags': ['星座运势', '每日运势', '星座', '占卜', '运势预测', '十二星座']
    }
    
    print(f"✅ 标题: {content['title']}")
    print(f"✅ 描述长度: {len(content['desc'])} 字符")
    print(f"✅ 标签数量: {len(content['tags'])} 个")
    print(f"✅ 图片文件: {os.path.basename(content['image_path'])}")
    
    return content

def test_scheduler_config():
    """测试定时任务配置"""
    print("\n⏰ 测试定时任务配置...")
    
    try:
        from horoscope.config import PUBLISH_CONFIG
        
        print(f"✅ 生成时间: {PUBLISH_CONFIG['schedule_time']}")
        print(f"✅ 发布时间: {PUBLISH_CONFIG['publish_time']}")
        print(f"✅ 自动发布: {PUBLISH_CONFIG['enable_auto_publish']}")
        print(f"✅ 标签配置: {len(PUBLISH_CONFIG['tags'])} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 定时任务配置出错: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 星座运势自动化系统完整测试")
    print("=" * 60)
    
    # 1. 测试星座运势生成
    image_path = test_horoscope_generation()
    if not image_path:
        print("\n❌ 星座运势生成测试失败")
        return
    
    # 2. 测试账号状态
    accounts = test_account_status()
    if not accounts:
        print("\n❌ 账号状态测试失败")
        return
    
    # 3. 测试Cookie验证
    valid_accounts = await test_cookie_validation(accounts)
    if not valid_accounts:
        print("\n❌ Cookie验证测试失败")
        return
    
    # 4. 测试内容准备
    content = test_content_preparation(image_path)
    if not content:
        print("\n❌ 内容准备测试失败")
        return
    
    # 5. 测试定时任务配置
    scheduler_ok = test_scheduler_config()
    if not scheduler_ok:
        print("\n❌ 定时任务配置测试失败")
        return
    
    # 测试总结
    print("\n🎉 自动化系统测试完成!")
    print("=" * 40)
    print("✅ 星座运势生成: 正常")
    print(f"✅ 有效账号数量: {len(valid_accounts)}")
    print("✅ Cookie验证: 通过")
    print("✅ 内容准备: 完成")
    print("✅ 定时任务配置: 正常")
    
    print("\n📊 系统状态总览:")
    print("=" * 30)
    print(f"🔮 星座数据: 12个星座完整配置")
    print(f"🎨 图片生成: 支持1080x1350分辨率")
    print(f"📱 发布平台: 小红书 ({len(valid_accounts)}个账号)")
    print(f"⏰ 定时任务: 每日17:00生成，次日08:00发布")
    print(f"🤖 AI生成: 需要配置OpenAI API Key")
    
    print("\n🚀 下一步操作:")
    print("1. 配置OpenAI API Key进行AI生成测试")
    print("2. 启动定时任务: python horoscope/scheduler.py")
    print("3. 手动发布测试: python test_xiaohongshu_publish.py")
    print("4. 查看生成的图片效果")
    
    # 创建状态报告
    create_status_report(image_path, valid_accounts, content)

def create_status_report(image_path, accounts, content):
    """创建状态报告"""
    report = f"""# 星座运势自动化系统测试报告

## 测试时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 测试结果
- ✅ 星座运势生成: 成功
- ✅ 图片生成: 成功 ({os.path.basename(image_path)})
- ✅ 账号验证: {len(accounts)}个有效账号
- ✅ 内容准备: 完成
- ✅ 系统配置: 正常

## 生成的内容
**标题**: {content['title']}
**图片**: {os.path.basename(image_path)}
**标签**: {', '.join(content['tags'])}

## 账号状态
"""
    
    for account in accounts:
        report += f"- {account[3]}: Cookie有效\n"
    
    report += f"""
## 下一步
1. 配置AI生成功能
2. 启动定时任务
3. 测试自动发布

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    with open('horoscope_test_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📄 测试报告已保存: horoscope_test_report.md")

if __name__ == "__main__":
    asyncio.run(main())
