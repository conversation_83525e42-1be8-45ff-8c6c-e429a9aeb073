# -*- coding: utf-8 -*-
"""
星座运势配置文件
"""

# 星座配置
ZODIAC_SIGNS = {
    'aries': {'name': '白羊座', 'date_range': '3.21-4.19', 'emoji': '♈'},
    'taurus': {'name': '金牛座', 'date_range': '4.20-5.20', 'emoji': '♉'},
    'gemini': {'name': '双子座', 'date_range': '5.21-6.21', 'emoji': '♊'},
    'cancer': {'name': '巨蟹座', 'date_range': '6.22-7.22', 'emoji': '♋'},
    'leo': {'name': '狮子座', 'date_range': '7.23-8.22', 'emoji': '♌'},
    'virgo': {'name': '处女座', 'date_range': '8.23-9.22', 'emoji': '♍'},
    'libra': {'name': '天秤座', 'date_range': '9.23-10.23', 'emoji': '♎'},
    'scorpio': {'name': '天蝎座', 'date_range': '10.24-11.22', 'emoji': '♏'},
    'sagittarius': {'name': '射手座', 'date_range': '11.23-12.21', 'emoji': '♐'},
    'capricorn': {'name': '摩羯座', 'date_range': '12.22-1.19', 'emoji': '♑'},
    'aquarius': {'name': '水瓶座', 'date_range': '1.20-2.18', 'emoji': '♒'},
    'pisces': {'name': '双鱼座', 'date_range': '2.19-3.20', 'emoji': '♓'}
}

# AI配置
AI_CONFIG = {
    'provider': 'openai',  # 可选: openai, claude, qwen
    'api_key': 'your-openai-api-key-here',  # 请填入您的API密钥
    'model': 'gpt-3.5-turbo',  # 或 gpt-4
    'base_url': 'https://api.openai.com/v1',  # 如果使用代理或其他服务
}

# 图片生成配置
IMAGE_CONFIG = {
    'width': 1080,
    'height': 1350,  # 小红书推荐比例 4:5
    'background_color': '#f8f9fa',
    'title_font_size': 48,
    'content_font_size': 28,
    'date_font_size': 24,
    'margin': 60,
    'line_spacing': 1.5,
}

# 发布配置
PUBLISH_CONFIG = {
    'schedule_time': '17:00',  # 每天下午5点生成
    'publish_time': '08:00',   # 第二天早上8点发布
    'title_template': '🌟 {date} 十二星座运势 🌟',
    'tags': ['星座运势', '每日运势', '星座', '占卜', '运势预测', '十二星座'],
    'enable_auto_publish': True,
}

# 模板配置
TEMPLATE_CONFIG = {
    'background_images': [
        'templates/bg1.jpg',
        'templates/bg2.jpg', 
        'templates/bg3.jpg',
    ],
    'fonts': {
        'title': 'fonts/SourceHanSansCN-Bold.otf',
        'content': 'fonts/SourceHanSansCN-Regular.otf',
        'date': 'fonts/SourceHanSansCN-Medium.otf',
    }
}
