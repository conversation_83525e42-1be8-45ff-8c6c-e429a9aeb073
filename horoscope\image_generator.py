# -*- coding: utf-8 -*-
"""
星座运势图片生成器
"""

import os
import platform
from PIL import Image, ImageDraw, ImageFont
from datetime import datetime
from typing import Dict, Tuple
import textwrap
from .config import IMAGE_CONFIG, TEMPLATE_CONFIG


class HoroscopeImageGenerator:
    def __init__(self):
        self.width = IMAGE_CONFIG['width']
        self.height = IMAGE_CONFIG['height']
        self.bg_color = IMAGE_CONFIG['background_color']
        self.margin = IMAGE_CONFIG['margin']
        
        # 确保字体文件存在
        self._ensure_fonts()
    
    def _ensure_fonts(self):
        """确保字体文件存在，如果不存在则使用系统默认字体"""
        self.fonts = {}

        # 获取系统字体路径
        system_fonts = self._get_system_fonts()

        # 尝试加载字体，按优先级顺序
        font_priorities = [
            TEMPLATE_CONFIG['fonts']['title'],  # 配置的字体
            *system_fonts,  # 系统字体
        ]

        # 加载标题字体
        self.fonts['title'] = self._load_font_with_fallback(
            font_priorities, IMAGE_CONFIG['title_font_size']
        )

        # 加载内容字体
        self.fonts['content'] = self._load_font_with_fallback(
            font_priorities, IMAGE_CONFIG['content_font_size']
        )

        # 加载日期字体
        self.fonts['date'] = self._load_font_with_fallback(
            font_priorities, IMAGE_CONFIG['date_font_size']
        )

    def _get_system_fonts(self):
        """获取系统中文字体路径"""
        system = platform.system()
        fonts = []

        if system == "Windows":
            fonts = [
                "C:/Windows/Fonts/msyh.ttc",      # 微软雅黑
                "C:/Windows/Fonts/simhei.ttf",    # 黑体
                "C:/Windows/Fonts/simsun.ttc",    # 宋体
                "C:/Windows/Fonts/simkai.ttf",    # 楷体
            ]
        elif system == "Darwin":  # macOS
            fonts = [
                "/System/Library/Fonts/PingFang.ttc",
                "/System/Library/Fonts/Helvetica.ttc",
                "/System/Library/Fonts/Arial.ttf",
            ]
        else:  # Linux
            fonts = [
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
            ]

        return fonts

    def _load_font_with_fallback(self, font_paths, size):
        """尝试加载字体，失败则使用下一个"""
        for font_path in font_paths:
            try:
                if os.path.exists(font_path):
                    return ImageFont.truetype(font_path, size)
            except Exception as e:
                print(f"无法加载字体 {font_path}: {e}")
                continue

        # 所有字体都失败，使用默认字体
        print("使用默认字体")
        return ImageFont.load_default()
    
    def generate_horoscope_image(self, horoscope_data: Dict, output_path: str) -> str:
        """
        生成星座运势图片
        
        Args:
            horoscope_data: 星座运势数据
            output_path: 输出文件路径
            
        Returns:
            str: 生成的图片文件路径
        """
        # 创建画布
        image = Image.new('RGB', (self.width, self.height), self.bg_color)
        draw = ImageDraw.Draw(image)
        
        # 获取日期
        date_str = list(horoscope_data.values())[0]['date']
        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        formatted_date = date_obj.strftime('%Y年%m月%d日')
        
        # 绘制标题
        title = f"🌟 {formatted_date} 十二星座运势 🌟"
        self._draw_title(draw, title)
        
        # 绘制星座运势
        y_offset = 120
        signs_per_row = 2
        sign_width = (self.width - 3 * self.margin) // signs_per_row
        sign_height = 180
        
        signs = list(horoscope_data.items())
        
        for i, (sign_key, sign_data) in enumerate(signs):
            row = i // signs_per_row
            col = i % signs_per_row
            
            x = self.margin + col * (sign_width + self.margin)
            y = y_offset + row * (sign_height + 20)
            
            self._draw_sign_card(draw, sign_data, x, y, sign_width, sign_height)
        
        # 绘制底部装饰
        self._draw_footer(draw)
        
        # 保存图片
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        image.save(output_path, 'JPEG', quality=95)
        
        return output_path
    
    def _draw_title(self, draw: ImageDraw, title: str):
        """绘制标题"""
        # 计算标题位置（居中）
        bbox = draw.textbbox((0, 0), title, font=self.fonts['title'])
        title_width = bbox[2] - bbox[0]
        title_x = (self.width - title_width) // 2
        title_y = 30
        
        # 绘制标题背景
        padding = 20
        draw.rounded_rectangle(
            [title_x - padding, title_y - 10, title_x + title_width + padding, title_y + 60],
            radius=15,
            fill='#4a90e2',
            outline='#357abd',
            width=2
        )
        
        # 绘制标题文字
        draw.text((title_x, title_y), title, font=self.fonts['title'], fill='white')
    
    def _draw_sign_card(self, draw: ImageDraw, sign_data: Dict, x: int, y: int, width: int, height: int):
        """绘制单个星座卡片"""
        # 绘制卡片背景
        draw.rounded_rectangle(
            [x, y, x + width, y + height],
            radius=10,
            fill='white',
            outline='#e0e0e0',
            width=1
        )
        
        # 绘制星座名称和图标
        sign_title = f"{sign_data['emoji']} {sign_data['name']}"
        sign_title += f" ({sign_data['date_range']})"
        
        # 计算文字位置
        text_x = x + 15
        text_y = y + 15
        
        # 绘制星座标题
        draw.text((text_x, text_y), sign_title, font=self.fonts['content'], fill='#2c3e50')
        
        # 绘制运势内容
        content_y = text_y + 35
        line_height = 25
        
        contents = [
            f"💫 整体：{sign_data['overall']}",
            f"💕 爱情：{sign_data['love']}",
            f"💼 事业：{sign_data['career']}",
            f"💰 财运：{sign_data['money']}",
            f"🏃 健康：{sign_data['health']}"
        ]
        
        for i, content in enumerate(contents):
            # 文字换行处理
            wrapped_text = self._wrap_text(content, width - 30)
            for j, line in enumerate(wrapped_text):
                draw.text(
                    (text_x, content_y + i * line_height + j * 20), 
                    line, 
                    font=self.fonts['date'], 
                    fill='#34495e'
                )
    
    def _wrap_text(self, text: str, max_width: int) -> list:
        """文字换行处理"""
        # 简单的换行处理，可以根据需要优化
        if len(text) * 12 <= max_width:  # 估算字符宽度
            return [text]
        
        # 按字符数分割
        chars_per_line = max_width // 12
        lines = []
        for i in range(0, len(text), chars_per_line):
            lines.append(text[i:i + chars_per_line])
        
        return lines
    
    def _draw_footer(self, draw: ImageDraw):
        """绘制底部装饰"""
        footer_y = self.height - 60
        footer_text = "✨ 仅供娱乐参考，愿你每天都有好运气 ✨"
        
        # 计算居中位置
        bbox = draw.textbbox((0, 0), footer_text, font=self.fonts['date'])
        text_width = bbox[2] - bbox[0]
        text_x = (self.width - text_width) // 2
        
        draw.text((text_x, footer_y), footer_text, font=self.fonts['date'], fill='#7f8c8d')


# 使用示例
if __name__ == "__main__":
    # 测试数据
    test_data = {
        'aries': {
            'name': '白羊座',
            'emoji': '♈',
            'date_range': '3.21-4.19',
            'date': '2024-01-15',
            'overall': '今日运势不错，保持积极心态',
            'love': '感情方面需要多沟通',
            'career': '工作上要专注细节',
            'money': '财运平稳，理性消费',
            'health': '注意休息，保持健康'
        }
        # ... 其他星座数据
    }
    
    generator = HoroscopeImageGenerator()
    output_file = "test_horoscope.jpg"
    generator.generate_horoscope_image(test_data, output_file)
    print(f"图片已生成: {output_file}")
