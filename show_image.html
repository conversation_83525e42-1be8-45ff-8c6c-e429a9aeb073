<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星座运势图片预览</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            max-width: 1200px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
            margin: 20px;
        }
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        .horoscope-image {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .status {
            color: #28a745;
            font-weight: bold;
        }
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .refresh-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🌟 星座运势图片预览 🌟</h1>
        
        <div class="info">
            <p><span class="status">✅ 字体修复测试完成！</span></p>
            <p>📅 生成时间: <span id="current-time"></span></p>
            <p>📍 文件位置: videoFile/horoscope/font_test.jpg</p>
            <p>🎨 字体问题已修复，使用微软雅黑字体显示中文</p>
            <p>📊 文件大小: 161,761 字节</p>
        </div>

        <div class="image-container">
            <h3>🆕 最新字体修复测试图片</h3>
            <img src="videoFile/horoscope/font_test.jpg"
                 alt="字体修复测试图片"
                 class="horoscope-image"
                 onerror="this.style.display='none'; document.getElementById('error-msg1').style.display='block';">

            <div id="error-msg1" style="display:none; color:red; padding:20px;">
                ❌ 无法加载新测试图片
            </div>

            <h3>📋 原始测试图片</h3>
            <img src="videoFile/horoscope/test_horoscope_20250615.jpg"
                 alt="原始星座运势图片"
                 class="horoscope-image"
                 style="margin-top: 20px;"
                 onerror="this.style.display='none'; document.getElementById('error-msg2').style.display='block';">

            <div id="error-msg2" style="display:none; color:red; padding:20px;">
                ❌ 无法加载原始图片
            </div>
        </div>
        
        <div style="text-align: center;">
            <button class="refresh-btn" onclick="location.reload()">🔄 刷新页面</button>
            <button class="refresh-btn" onclick="generateNew()">🎲 生成新图片</button>
        </div>
        
        <div class="info">
            <h3>📝 测试说明</h3>
            <ul>
                <li>如果图片显示正常且中文清晰，说明字体问题已解决</li>
                <li>如果仍有乱码，请检查系统是否安装了微软雅黑字体</li>
                <li>图片尺寸为 1080x1350，适合小红书发布</li>
                <li>包含12个星座的完整运势信息</li>
            </ul>
        </div>
    </div>

    <script>
        // 显示当前时间
        document.getElementById('current-time').textContent = new Date().toLocaleString('zh-CN');
        
        // 生成新图片
        function generateNew() {
            alert('请在命令行运行: python test_horoscope.py');
        }
        
        // 每5秒检查一次图片是否更新
        setInterval(function() {
            const img = document.querySelector('.horoscope-image');
            if (img) {
                img.src = img.src.split('?')[0] + '?' + new Date().getTime();
            }
        }, 5000);
    </script>
</body>
</html>
