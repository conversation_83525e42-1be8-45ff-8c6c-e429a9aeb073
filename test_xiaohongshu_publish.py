#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试小红书发布功能
"""

import os
import sys
import sqlite3
import shutil
from datetime import datetime
import asyncio

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_valid_account():
    """获取有效的小红书账号"""
    try:
        conn = sqlite3.connect('db/user_info.db')
        cursor = conn.cursor()
        
        # 查询有效的小红书账号 (type=1, status=1)
        cursor.execute('''
            SELECT * FROM user_info 
            WHERE type = 1 AND status = 1
            LIMIT 1
        ''')
        
        account = cursor.fetchone()
        conn.close()
        
        if account:
            return {
                'id': account[0],
                'type': account[1],
                'cookie_file': account[2],
                'username': account[3],
                'status': account[4]
            }
        return None
        
    except Exception as e:
        print(f"❌ 获取账号信息失败: {e}")
        return None

def prepare_test_content():
    """准备测试内容"""
    # 使用最新生成的星座图片
    image_dir = "videoFile/horoscope"
    if not os.path.exists(image_dir):
        print(f"❌ 图片目录不存在: {image_dir}")
        return None
    
    # 查找最新的图片文件
    image_files = [f for f in os.listdir(image_dir) if f.endswith('.jpg')]
    if not image_files:
        print(f"❌ 没有找到图片文件")
        return None
    
    # 按修改时间排序，获取最新的
    image_files.sort(key=lambda x: os.path.getmtime(os.path.join(image_dir, x)), reverse=True)
    latest_image = image_files[0]
    image_path = os.path.join(image_dir, latest_image)
    
    # 准备发布内容
    today = datetime.now().strftime('%Y年%m月%d日')
    content = {
        'title': f'🌟 {today} 十二星座运势 🌟',
        'desc': f'''✨ {today} 星座运势预测 ✨

🔮 今日为您带来十二星座的详细运势分析
💫 包含整体运势、爱情、事业、财运、健康五大方面

⭐ 每日更新，仅供娱乐参考
🌙 愿你每天都有好运气！

#星座运势 #每日运势 #星座 #占卜 #运势预测 #十二星座''',
        'image_path': image_path,
        'tags': ['星座运势', '每日运势', '星座', '占卜', '运势预测', '十二星座']
    }
    
    return content

async def test_xiaohongshu_publish():
    """测试小红书发布"""
    print("🚀 开始小红书发布测试...")
    print("=" * 50)
    
    # 1. 检查账号
    print("📱 检查账号信息...")
    account = get_valid_account()
    if not account:
        print("❌ 没有找到有效的小红书账号")
        return False
    
    print(f"✅ 找到有效账号: {account['username']}")
    print(f"   Cookie文件: {account['cookie_file']}")
    
    # 2. 准备内容
    print("\n📝 准备发布内容...")
    content = prepare_test_content()
    if not content:
        print("❌ 准备发布内容失败")
        return False
    
    print(f"✅ 内容准备完成:")
    print(f"   标题: {content['title']}")
    print(f"   图片: {content['image_path']}")
    print(f"   图片大小: {os.path.getsize(content['image_path']):,} 字节")
    
    # 3. 导入发布模块
    print("\n🔧 导入发布模块...")
    try:
        from uploader.xiaohongshu_uploader.main import XiaoHongShuVideo, cookie_auth
        from myUtils.postVideo import post_video_xhs
        print("✅ 小红书发布模块导入成功")
    except ImportError as e:
        print(f"❌ 导入发布模块失败: {e}")
        print("💡 请确保已安装所有依赖包")
        return False
    
    # 4. 设置发布参数
    print("\n⚙️  配置发布参数...")
    try:
        cookie_path = f"cookiesFile/{account['cookie_file']}"
        
        # 检查cookie文件
        if not os.path.exists(cookie_path):
            print(f"❌ Cookie文件不存在: {cookie_path}")
            return False
        
        print(f"✅ Cookie文件存在: {cookie_path}")
        
        # 设置发布参数
        publish_config = {
            'title': content['title'],
            'desc': content['desc'],
            'image_path': content['image_path'],
            'cookie_path': cookie_path,
            'tags': content['tags']
        }
        
        print("✅ 发布参数配置完成")
        
    except Exception as e:
        print(f"❌ 配置发布参数失败: {e}")
        return False
    
    # 5. 执行发布测试
    print("\n🚀 开始发布测试...")
    print("⚠️  注意: 这是真实发布，将会发布到您的小红书账号")
    
    # 询问用户确认
    confirm = input("\n❓ 确认要进行真实发布测试吗? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 用户取消发布测试")
        return False
    
    try:
        print("📤 正在发布到小红书...")

        # 首先验证cookie是否有效
        print("🔐 验证Cookie有效性...")
        cookie_valid = await cookie_auth(cookie_path)
        if not cookie_valid:
            print("❌ Cookie已失效，请重新登录获取Cookie")
            return False

        print("✅ Cookie验证通过")

        # 准备发布文件
        print("📁 准备发布文件...")

        # 确保图片在videoFile目录中
        video_dir = "videoFile"
        os.makedirs(video_dir, exist_ok=True)

        # 复制图片到videoFile目录
        import shutil
        image_filename = f"horoscope_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
        target_path = os.path.join(video_dir, image_filename)
        shutil.copy2(content['image_path'], target_path)

        print(f"✅ 图片已复制到: {target_path}")

        # 使用项目的发布函数
        print("🚀 开始真实发布...")

        # 直接使用XiaoHongShuVideo类
        from pathlib import Path
        from conf import BASE_DIR

        # 构建完整路径
        file_path = Path(BASE_DIR / "videoFile" / image_filename)
        cookie_path = Path(BASE_DIR / "cookiesFile" / account['cookie_file'])

        # 创建发布实例
        app = XiaoHongShuVideo(
            title=content['title'],
            file_path=file_path,
            tags=content['tags'],
            publish_date=0,  # 立即发布
            account_file=cookie_path
        )

        # 执行发布
        await app.main()

        print("✅ 发布请求已提交!")
        print("📱 请检查您的小红书账号确认发布状态")

        return True

    except Exception as e:
        print(f"❌ 发布失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        # 运行异步测试
        result = asyncio.run(test_xiaohongshu_publish())
        
        if result:
            print("\n🎉 小红书发布测试完成!")
            print("✅ 所有功能正常，可以进行自动发布")
            print("\n📝 下一步:")
            print("1. 配置定时任务自动生成和发布")
            print("2. 运行 python horoscope/scheduler.py 启动定时服务")
        else:
            print("\n❌ 发布测试失败")
            print("💡 请检查账号配置和网络连接")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")

if __name__ == "__main__":
    main()
