#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查小红书账号信息
"""

import sqlite3
import json
import os

def check_accounts():
    """检查数据库中的账号信息"""
    print("📋 检查小红书账号信息...")
    print("=" * 50)
    
    try:
        # 连接数据库
        conn = sqlite3.connect('db/user_info.db')
        cursor = conn.cursor()
        
        # 查询所有账号
        cursor.execute('SELECT * FROM user_info')
        rows = cursor.fetchall()
        
        if not rows:
            print("❌ 数据库中没有找到任何账号信息")
            print("💡 请在前端界面添加小红书账号")
            return False
        
        print(f"✅ 找到 {len(rows)} 个账号:")
        
        valid_accounts = []
        for i, row in enumerate(rows, 1):
            print(f"\n📱 账号 {i}:")
            print(f"   ID: {row[0]}")
            print(f"   平台: {row[1]}")
            print(f"   Cookie文件: {row[2]}")
            print(f"   用户名: {row[3]}")
            print(f"   状态: {'✅ 有效' if row[4] == 1 else '❌ 无效'}")
            
            # 检查cookie文件是否存在
            cookie_path = f"cookiesFile/{row[2]}"
            if os.path.exists(cookie_path):
                print(f"   Cookie文件: ✅ 存在 ({cookie_path})")
                if row[4] == 1:  # 状态有效
                    valid_accounts.append(row)
            else:
                print(f"   Cookie文件: ❌ 不存在 ({cookie_path})")
        
        conn.close()
        
        if valid_accounts:
            print(f"\n🎉 找到 {len(valid_accounts)} 个有效账号可用于发布测试")
            return valid_accounts
        else:
            print("\n⚠️  没有找到有效的账号用于发布")
            return False
            
    except Exception as e:
        print(f"❌ 检查账号时出错: {e}")
        return False

def check_cookie_files():
    """检查cookie文件目录"""
    print("\n📁 检查Cookie文件目录...")
    
    cookie_dir = "cookiesFile"
    if not os.path.exists(cookie_dir):
        print(f"❌ Cookie目录不存在: {cookie_dir}")
        return
    
    files = os.listdir(cookie_dir)
    if not files:
        print(f"📂 Cookie目录为空: {cookie_dir}")
        return
    
    print(f"📂 Cookie目录 ({cookie_dir}) 包含 {len(files)} 个文件:")
    for file in files:
        file_path = os.path.join(cookie_dir, file)
        file_size = os.path.getsize(file_path)
        print(f"   📄 {file} ({file_size:,} 字节)")

if __name__ == "__main__":
    # 检查账号信息
    accounts = check_accounts()
    
    # 检查cookie文件
    check_cookie_files()
    
    if accounts:
        print(f"\n✅ 账号检查完成，可以进行发布测试")
        print(f"🚀 下一步: 运行发布测试脚本")
    else:
        print(f"\n❌ 请先在前端界面正确添加小红书账号")
